'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import AddressSearch from '../../components/AddressSearch';
import UserDataExport from '../../components/UserDataExport';

export default function ProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [userDetails, setUserDetails] = useState({
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    latitude: null as number | null,
    longitude: null as number | null
  });
  const [dbUser, setDbUser] = useState<any>(null);

  // Načítanie údajov používateľa z databázy
  const fetchUserDetails = async () => {
    if (!user) return;

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) {
        throw new Error('Chyba pri získavaní autentifikačného tokenu');
      }
      const token = await user.getIdToken();

      // Logovanie pre debugovanie - len základné informácie bez citlivých údajov
      console.log('Používateľ pri načítaní - základné info:', {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        hashedUserId: user.hashedUserId
      });

      // Použitie hashedUserId pre API volanie
      const userId = user.hashedUserId;
      if (!userId) {
        throw new Error('Chýba hashedUserId používateľa');
      }
      console.log('Použité ID pre API volanie pri načítaní:', userId);
      console.log('user.hashedUserId:', user.hashedUserId);

      // Použitie hashedUserId pre API volanie
      // Hashované ID je bezpečnejšie ako číselné ID alebo Firebase UID
      const response = await fetch(`/api/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setDbUser(userData);
        setUserDetails({
          phone: userData.phone || '',
          address: userData.address || '',
          city: userData.city || '',
          postalCode: userData.postalCode || '',
          latitude: userData.latitude || null,
          longitude: userData.longitude || null
        });
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Nepodarilo sa načítať údaje používateľa' }));
        console.error('Chyba pri načítaní údajov používateľa:', errorData.error);

        // Ak je chyba 404 (používateľ nebol nájdený), pokúsime sa vytvoriť prepojenie
        if (response.status === 404 && user.email) {
          console.log('Používateľ nebol nájdený, pokúšam sa vytvoriť prepojenie...');
          await createUserLinkage();
        } else {
          toast.error(errorData.error || 'Nepodarilo sa načítať údaje používateľa');
        }
      }
    } catch (error) {
      console.error('Chyba pri načítaní údajov používateľa:', error);
      toast.error('Nastala chyba pri načítaní údajov používateľa');
    }
  };

  // Funkcia pre vytvorenie prepojenia medzi Firebase a databázou
  const createUserLinkage = async () => {
    if (!user || !user.email) return;

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) {
        throw new Error('Chyba pri získavaní autentifikačného tokenu');
      }
      const token = await user.getIdToken();

      // Najprv skúsime nájsť používateľa podľa emailu
      const emailResponse = await fetch(`/api/users/email/${encodeURIComponent(user.email)}`, {
        headers: {
          'x-auth-request': 'true'
        }
      });

      if (emailResponse.ok) {
        // Používateľ existuje, aktualizujeme jeho Firebase UID
        const userData = await emailResponse.json();

        // Aktualizácia používateľa - použitie hashedId namiesto firebaseUid
        const userId = userData.hashedId || userData.id.toString();
        const updateResponse = await fetch(`/api/users/${userId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            // Už nepoužívame firebaseUid, ale môžeme aktualizovať iné údaje
            name: user.displayName || userData.name || 'Používateľ'
          }),
        });

        if (updateResponse.ok) {
          console.log('Prepojenie používateľa bolo úspešne vytvorené');
          // Aktualizácia lokálnych údajov
          const updatedUser = await updateResponse.json();
          setDbUser(updatedUser);
          setUserDetails({
            phone: updatedUser.phone || '',
            address: updatedUser.address || '',
            city: updatedUser.city || '',
            postalCode: updatedUser.postalCode || '',
            latitude: updatedUser.latitude || null,
            longitude: updatedUser.longitude || null
          });

          // Aktualizácia hashedUserId v objekte user
          if (user && updatedUser.id) {
            const authUser = user as any;
            authUser.hashedUserId = updatedUser.hashedId;
            console.log('hashedUserId nastavený na:', updatedUser.hashedId);
          }
        } else {
          console.error('Nepodarilo sa aktualizovať Firebase UID používateľa');
        }
      } else {
        // Používateľ neexistuje, vytvoríme nového
        const createResponse = await fetch('/api/users', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: user.email,
            name: user.displayName || 'Používateľ',
            role: 'USER',
            password: '',
            // Už nepoužívame firebaseUid
          }),
        });

        if (createResponse.ok) {
          console.log('Nový používateľ bol úspešne vytvorený');
          // Aktualizácia lokálnych údajov
          const newUser = await createResponse.json();
          setDbUser(newUser);
          setUserDetails({
            phone: '',
            address: '',
            city: '',
            postalCode: '',
            latitude: null,
            longitude: null
          });

          // Aktualizácia hashedUserId v objekte user
          if (user && newUser.id) {
            const authUser = user as any;
            authUser.hashedUserId = newUser.hashedId;
            console.log('hashedUserId nastavený na (nový používateľ):', newUser.hashedId);
          }
        } else {
          console.error('Nepodarilo sa vytvoriť nového používateľa');
        }
      }
    } catch (error) {
      console.error('Chyba pri vytváraní prepojenia:', error);
    }
  };

  // Presmerovanie na domovskú stránku, ak používateľ nie je prihlásený
  useEffect(() => {
    if (!user && !isLoading) {
      router.push('/');
    } else if (user) {
      fetchUserDetails();
      setIsLoading(false);
    }
  }, [user, router, isLoading]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam profil...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  // Formátovanie dátumu vytvorenia účtu
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('sk-SK', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Získanie názvu poskytovateľa autentifikácie
  const getProviderName = (providerId: string) => {
    switch (providerId) {
      case 'google.com':
        return 'Google';
      case 'facebook.com':
        return 'Facebook';
      default:
        return providerId;
    }
  };

  // Získanie názvu role
  const getRoleName = (role?: string) => {
    switch (role) {
      case 'admin':
        return 'Administrátor';
      case 'classic':
        return 'Používateľ';
      case 'none':
        return 'Žiadna rola';
      default:
        return 'Neznáma rola';
    }
  };

  // Funkcia na spracovanie zmien vo formulári
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Funkcia na uloženie zmien
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !dbUser) return;

    setIsSaving(true);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) {
        throw new Error('Chyba pri získavaní autentifikačného tokenu');
      }
      const token = await user.getIdToken();

      // Logovanie pre debugovanie - len základné informácie bez citlivých údajov
      console.log('Používateľ - základné info:', {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        hashedUserId: user.hashedUserId,
      });
      console.log('dbUser ID:', dbUser?.id);
      console.log('userDetails - základné info:', userDetails ? 'existuje' : 'neexistuje');

      // Použitie hashedUserId pre API volanie
      // Hashované ID je bezpečnejšie ako číselné ID alebo Firebase UID
      const userId = user.hashedUserId || user.uid;
      console.log('Použité ID pre API volanie:', userId);

      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(userDetails),
      });

      if (response.ok) {
        toast.success('Údaje boli úspešne aktualizované');
        // Aktualizácia lokálnych údajov
        const updatedUser = await response.json();
        setDbUser(updatedUser);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Nepodarilo sa aktualizovať údaje');
      }
    } catch (error) {
      console.error('Chyba pri aktualizácii údajov:', error);
      toast.error('Nastala chyba pri aktualizácii údajov');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Môj profil</h1>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6">
          {/* Hlavička profilu */}
          <div className="mb-6">
            <h2 className="text-2xl font-bold">{user.displayName || 'Používateľ'}</h2>
            <p className="text-neutral-500 mt-1">{user.email}</p>
            <div className="mt-2">
              <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                user.role === 'admin' ? 'bg-red-100 text-red-800' :
                user.role === 'classic' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {getRoleName(user.role)}
              </span>
            </div>
          </div>

          {/* Informácie o účte */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-neutral-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Informácie o účte</h3>
              <ul className="space-y-2">
                <li className="flex justify-between">
                  <span className="text-neutral-500">ID používateľa:</span>
                  <span className="font-medium">{user.uid ? user.uid.substring(0, 8) + '...' : 'Neznáme ID'}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-neutral-500">Poskytovateľ:</span>
                  <span className="font-medium">{getProviderName(user.providerData && user.providerData[0]?.providerId || '')}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-neutral-500">Vytvorené:</span>
                  <span className="font-medium">{formatDate(user.metadata && user.metadata.creationTime ? Date.parse(user.metadata.creationTime) : Date.now())}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-neutral-500">Posledné prihlásenie:</span>
                  <span className="font-medium">{formatDate(user.metadata && user.metadata.lastSignInTime ? Date.parse(user.metadata.lastSignInTime) : Date.now())}</span>
                </li>
              </ul>
            </div>

            <div className="bg-neutral-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Kontaktné údaje</h3>
              <ul className="space-y-2">
                <li className="flex justify-between">
                  <span className="text-neutral-500">Email:</span>
                  <span className="font-medium">{user.email}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-neutral-500">Email overený:</span>
                  <span className={`font-medium ${user.emailVerified ? 'text-green-600' : 'text-red-600'}`}>
                    {user.emailVerified ? 'Áno' : 'Nie'}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-neutral-500">Telefón:</span>
                  <span className="font-medium">{dbUser?.phone || 'Nenastavený'}</span>
                </li>
                {dbUser?.address && (
                  <li className="flex justify-between">
                    <span className="text-neutral-500">Adresa:</span>
                    <span className="font-medium">{dbUser.address}</span>
                  </li>
                )}
                {dbUser?.city && (
                  <li className="flex justify-between">
                    <span className="text-neutral-500">Mesto:</span>
                    <span className="font-medium">{dbUser.city}</span>
                  </li>
                )}
                {dbUser?.postalCode && (
                  <li className="flex justify-between">
                    <span className="text-neutral-500">PSČ:</span>
                    <span className="font-medium">{dbUser.postalCode}</span>
                  </li>
                )}
              </ul>
            </div>
          </div>

          {/* Formulár na editáciu kontaktných údajov */}
          <div className="bg-neutral-50 p-4 rounded-lg mt-6">
            <h3 className="text-lg font-semibold mb-4">Editácia kontaktných údajov</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-neutral-700 mb-1">
                    Telefón
                  </label>
                  <input
                    type="text"
                    id="phone"
                    name="phone"
                    value={userDetails.phone}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Zadajte telefónne číslo"
                  />
                </div>

                <div className="col-span-1 md:col-span-2">
                  <label htmlFor="address" className="block text-sm font-medium text-neutral-700 mb-1">
                    Adresa
                  </label>
                  <AddressSearch
                    initialAddress={userDetails.address}
                    onAddressSelect={(address, latitude, longitude, city, postalCode) => {
                      setUserDetails(prev => ({
                        ...prev,
                        address,
                        latitude,
                        longitude,
                        city: city || prev.city,
                        postalCode: postalCode || prev.postalCode
                      }));
                    }}
                    placeholder="Vyhľadajte adresu..."
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                  <p className="text-xs text-neutral-500 mt-1">
                    Vyhľadajte adresu pomocou OpenStreetMap. Zadajte aspoň 3 znaky a vyberte adresu zo zoznamu.
                  </p>
                </div>

                {userDetails.latitude && userDetails.longitude && (
                  <div className="col-span-1 md:col-span-2 bg-green-50 p-2 rounded-md">
                    <p className="text-sm text-green-700">
                      Adresa bola úspešne nájdená. Súradnice: {userDetails.latitude.toFixed(6)}, {userDetails.longitude.toFixed(6)}
                    </p>
                  </div>
                )}

                <div>
                  <label htmlFor="city" className="block text-sm font-medium text-neutral-700 mb-1">
                    Mesto (automaticky vyplnené z adresy)
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="city"
                      name="city"
                      value={userDetails.city || ''}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-neutral-50"
                      placeholder="Mesto sa vyplní automaticky"
                    />
                    {userDetails.city && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500">
                        ✓
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">
                    Toto pole sa automaticky vyplní po výbere adresy
                  </p>
                </div>

                <div>
                  <label htmlFor="postalCode" className="block text-sm font-medium text-neutral-700 mb-1">
                    PSČ (automaticky vyplnené z adresy)
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="postalCode"
                      name="postalCode"
                      value={userDetails.postalCode || ''}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-neutral-50"
                      placeholder="PSČ sa vyplní automaticky"
                    />
                    {userDetails.postalCode && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-green-500">
                        ✓
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-neutral-500 mt-1">
                    Toto pole sa automaticky vyplní po výbere adresy
                  </p>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSaving}
                  className="btn btn-primary font-semibold text-base px-6 py-3 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200 disabled:opacity-50 disabled:transform-none"
                >
                  {isSaving ? 'Ukladám...' : 'Uložiť zmeny'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* GDPR Data Export Section */}
      <div className="mt-8">
        <UserDataExport />
      </div>
    </div>
  );
}
