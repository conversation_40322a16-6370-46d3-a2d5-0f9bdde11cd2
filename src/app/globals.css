@import "tailwindcss";
@import "./styles/form-elements.css";
@import "./styles/mobile-form.css";
@import "./styles/mobile-header.css";

:root {
  /* Základné farby */
  --background: #ffffff;
  --foreground: #333333;

  /* P<PERSON>árna farebná paleta - jemné pastelové farby */
  --primary: #4ECDC4;      /* Tyrkysová - hlavná farba */
  --primary-light: #A7E8BD; /* Svet<PERSON> */
  --primary-dark: #1A535C;  /* Tmavo tyrkysová */

  /* Sekundárna farebná paleta */
  --secondary: #FF6B6B;    /* <PERSON><PERSON>ová - akcentová farba */
  --secondary-light: #FFE66D; /* <PERSON><PERSON><PERSON> */
  --secondary-dark: #F25F5C;  /* Tmavo <PERSON> */

  /* Neutrálne farby */
  --neutral-100: #f8f9fa;
  --neutral-200: #e9ecef;
  --neutral-300: #dee2e6;
  --neutral-400: #ced4da;
  --neutral-500: #adb5bd;
  --neutral-600: #6c757d;
  --neutral-700: #495057;
  --neutral-800: #343a40;
  --neutral-900: #212529;

  /* Stavové farby */
  --success: #A7E8BD;
  --warning: #FFE66D;
  --error: #F25F5C;
  --info: #4ECDC4;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans, Arial, Helvetica, sans-serif);
  /* Toy-themed background pattern */
  background-image: url("data:image/svg+xml,%3Csvg width='400' height='400' viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cstyle%3E.toy-pattern%7Bfill:none;stroke-width:1.5;opacity:0.08%7D.toy-bear%7Bstroke:%23adb5bd%7D.toy-block%7Bstroke:%234ECDC4%7D.toy-top%7Bstroke:%23FF6B6B%7D.toy-kite%7Bstroke:%23FFE66D%7D.toy-train%7Bstroke:%23A7E8BD%7D.toy-ball%7Bstroke:%23F25F5C%7D%3C/style%3E%3C/defs%3E%3C!-- Teddy Bears --%3E%3Cg class='toy-pattern toy-bear'%3E%3Ccircle cx='50' cy='40' r='15'/%3E%3Ccircle cx='40' cy='30' r='6'/%3E%3Ccircle cx='60' cy='30' r='6'/%3E%3Cellipse cx='50' cy='70' rx='12' ry='18'/%3E%3Ccircle cx='38' cy='85' r='8'/%3E%3Ccircle cx='62' cy='85' r='8'/%3E%3Ccircle cx='45' cy='40' r='2'/%3E%3Ccircle cx='55' cy='40' r='2'/%3E%3Cpath d='M47 45 Q50 48 53 45'/%3E%3C/g%3E%3Cg class='toy-pattern toy-bear' transform='translate(300,250)'%3E%3Ccircle cx='50' cy='40' r='15'/%3E%3Ccircle cx='40' cy='30' r='6'/%3E%3Ccircle cx='60' cy='30' r='6'/%3E%3Cellipse cx='50' cy='70' rx='12' ry='18'/%3E%3Ccircle cx='38' cy='85' r='8'/%3E%3Ccircle cx='62' cy='85' r='8'/%3E%3Ccircle cx='45' cy='40' r='2'/%3E%3Ccircle cx='55' cy='40' r='2'/%3E%3Cpath d='M47 45 Q50 48 53 45'/%3E%3C/g%3E%3C!-- Building Blocks --%3E%3Cg class='toy-pattern toy-block'%3E%3Crect x='150' y='50' width='30' height='30' rx='3'/%3E%3Ctext x='165' y='70' font-family='Arial' font-size='16' text-anchor='middle' fill='%234ECDC4' opacity='0.6'%3E1%3C/text%3E%3Crect x='120' y='80' width='30' height='30' rx='3'/%3E%3Ctext x='135' y='100' font-family='Arial' font-size='16' text-anchor='middle' fill='%234ECDC4' opacity='0.6'%3E2%3C/text%3E%3C/g%3E%3Cg class='toy-pattern toy-block' transform='translate(200,200)'%3E%3Crect x='150' y='50' width='30' height='30' rx='3'/%3E%3Ctext x='165' y='70' font-family='Arial' font-size='16' text-anchor='middle' fill='%234ECDC4' opacity='0.6'%3E3%3C/text%3E%3Crect x='120' y='80' width='30' height='30' rx='3'/%3E%3Ctext x='135' y='100' font-family='Arial' font-size='16' text-anchor='middle' fill='%234ECDC4' opacity='0.6'%3E4%3C/text%3E%3C/g%3E%3C!-- Spinning Tops --%3E%3Cg class='toy-pattern toy-top'%3E%3Ccircle cx='280' cy='80' r='12'/%3E%3Cpath d='M280 68 L280 92 M275 73 L285 73 M276 78 L284 78'/%3E%3Cpath d='M268 80 Q280 70 292 80'/%3E%3C/g%3E%3Cg class='toy-pattern toy-top' transform='translate(-150,180)'%3E%3Ccircle cx='280' cy='80' r='12'/%3E%3Cpath d='M280 68 L280 92 M275 73 L285 73 M276 78 L284 78'/%3E%3Cpath d='M268 80 Q280 70 292 80'/%3E%3C/g%3E%3C!-- Kites --%3E%3Cg class='toy-pattern toy-kite'%3E%3Cpath d='M80 150 L95 135 L110 150 L95 165 Z'/%3E%3Cpath d='M95 165 L95 185 M90 170 L100 170 M92 175 L98 175'/%3E%3Cpath d='M80 150 L110 150 M95 135 L95 165'/%3E%3C/g%3E%3Cg class='toy-pattern toy-kite' transform='translate(150,100)'%3E%3Cpath d='M80 150 L95 135 L110 150 L95 165 Z'/%3E%3Cpath d='M95 165 L95 185 M90 170 L100 170 M92 175 L98 175'/%3E%3Cpath d='M80 150 L110 150 M95 135 L95 165'/%3E%3C/g%3E%3C!-- Toy Trains --%3E%3Cg class='toy-pattern toy-train'%3E%3Crect x='200' y='320' width='40' height='20' rx='3'/%3E%3Ccircle cx='210' cy='345' r='6'/%3E%3Ccircle cx='230' cy='345' r='6'/%3E%3Crect x='245' y='325' width='30' height='15' rx='2'/%3E%3Ccircle cx='255' cy='345' r='5'/%3E%3Ccircle cx='265' cy='345' r='5'/%3E%3Cpath d='M200 330 L190 330 L185 325 L185 335 L190 330'/%3E%3C/g%3E%3Cg class='toy-pattern toy-train' transform='translate(-100,-150)'%3E%3Crect x='200' y='320' width='40' height='20' rx='3'/%3E%3Ccircle cx='210' cy='345' r='6'/%3E%3Ccircle cx='230' cy='345' r='6'/%3E%3Crect x='245' y='325' width='30' height='15' rx='2'/%3E%3Ccircle cx='255' cy='345' r='5'/%3E%3Ccircle cx='265' cy='345' r='5'/%3E%3Cpath d='M200 330 L190 330 L185 325 L185 335 L190 330'/%3E%3C/g%3E%3C!-- Balls --%3E%3Cg class='toy-pattern toy-ball'%3E%3Ccircle cx='350' cy='150' r='15'/%3E%3Cpath d='M335 150 Q350 135 365 150 M335 150 Q350 165 365 150 M350 135 L350 165'/%3E%3C/g%3E%3Cg class='toy-pattern toy-ball' transform='translate(-250,100)'%3E%3Ccircle cx='350' cy='150' r='15'/%3E%3Cpath d='M335 150 Q350 135 365 150 M335 150 Q350 165 365 150 M350 135 L350 165'/%3E%3C/g%3E%3C!-- Small decorative elements --%3E%3Cg class='toy-pattern' stroke='%23dee2e6' opacity='0.04'%3E%3Cpath d='M30 200 L35 195 L40 200 L35 205 Z'/%3E%3Cpath d='M320 50 L325 45 L330 50 L325 55 Z'/%3E%3Cpath d='M70 300 L75 295 L80 300 L75 305 Z'/%3E%3Cpath d='M250 180 L255 175 L260 180 L255 185 Z'/%3E%3Ccircle cx='380' cy='280' r='3'/%3E%3Ccircle cx='20' cy='120' r='3'/%3E%3Ccircle cx='180' cy='350' r='3'/%3E%3Cpath d='M300 300 Q305 295 310 300'/%3E%3Cpath d='M50 250 Q55 245 60 250'/%3E%3C/g%3E%3C/svg%3E");
  background-size: 400px 400px;
  background-repeat: repeat;
  background-attachment: fixed;
}

/* Základné komponenty */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary);
  color: white;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

/* Subtle delete button for toy cards */
.btn-delete-subtle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  padding: 0.375rem;
  background-color: transparent;
  border: 1px solid #fecaca;
  color: #dc2626;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  min-width: 32px;
  min-height: 32px;
  opacity: 0.8;
}

.btn-delete-subtle:hover {
  background-color: #fef2f2;
  border-color: #fca5a5;
  color: #b91c1c;
  opacity: 1;
  transform: scale(1.05);
}

.btn-delete-subtle:active {
  transform: scale(0.95);
}

.btn-delete-subtle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-delete-subtle:disabled:hover {
  background-color: transparent;
  border-color: #fecaca;
  color: #dc2626;
  transform: none;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Animácie */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-in-out;
}

/* Modern Footer Styles */
.footer-gradient-border {
  background: linear-gradient(90deg, transparent 0%, var(--neutral-200) 50%, transparent 100%);
  height: 1px;
}

/* Enhanced hover effects for footer links */
footer a:hover {
  transform: translateX(2px);
}

/* Smooth animations for footer elements */
footer .group:hover .bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient-shift 2s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Version badge pulse animation */
footer .inline-flex.items-center.px-3.py-1 {
  animation: subtle-pulse 3s ease-in-out infinite;
}

@keyframes subtle-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

/* Responzívne nastavenia */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  /* Mobile footer adjustments */
  footer .absolute.w-96.h-96 {
    width: 200px;
    height: 200px;
  }

  footer .grid {
    gap: 2rem;
  }

  /* Mobile toy detail image improvements */
  .toy-detail-image {
    min-height: 280px !important;
  }

  /* Ensure proper image aspect ratio on mobile */
  .toy-detail-image img {
    object-position: center center !important;
  }
}

/* Toy detail image enhancements */
.toy-detail-image {
  position: relative;
  background: linear-gradient(135deg, var(--neutral-100) 0%, var(--neutral-200) 100%);
}

.toy-detail-image img {
  transition: transform 0.3s ease-in-out;
}

.toy-detail-image:hover img {
  transform: scale(1.02);
}

/* Ensure images maintain quality at different sizes */
.toy-detail-image img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Desktop specific improvements */
@media (min-width: 769px) {
  .toy-detail-image {
    min-height: 384px; /* h-96 equivalent */
  }
}
