{"name": "src", "version": "0.34", "private": true, "scripts": {"dev": "node scripts/version-info.js && next dev --turbopack", "dev:webpack": "node scripts/version-info.js && next dev", "dev:turbo": "node scripts/version-info.js && next dev --turbopack", "dev:newrelic": "FORCE_NEW_RELIC=true node scripts/version-info.js && next dev --turbopack", "build": "node scripts/version-info.js && next build && node scripts/copy-standalone-assets.js", "build:only": "node scripts/version-info.js && next build", "postbuild": "node scripts/copy-standalone-assets.js", "start": "node scripts/version-info.js && node .next/standalone/server.js", "start:legacy": "node scripts/version-info.js && next start", "lint": "next lint", "prisma": "npx prisma generate"}, "dependencies": {"@firebase/auth": "^1.10.1", "@prisma/client": "^6.7.0", "@types/archiver": "^6.0.3", "@types/node-fetch": "^2.6.12", "@types/xmlbuilder": "^11.0.1", "archiver": "^7.0.1", "cloudinary": "^2.6.1", "dotenv": "^16.5.0", "firebase": "^11.6.1", "firebase-admin": "^13.4.0", "heic-convert": "^2.1.0", "mysql2": "^3.14.1", "newrelic": "^12.20.0", "next": "15.3.1", "node-fetch": "^2.7.0", "prisma": "^6.7.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-toastify": "^11.0.5", "resend": "^4.6.0", "sharp": "^0.33.5", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/newrelic": "^9.14.8", "@types/node": "^20.17.33", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "eslint": "^9", "eslint-config-next": "15.3.1", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "typescript": "^5.8.3"}}